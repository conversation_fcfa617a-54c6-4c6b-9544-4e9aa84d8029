// 文件存储抽象层接口
// 支持不同的存储服务提供商

export interface StorageFile {
  key: string;
  url: string;
  size: number;
  contentType: string;
  lastModified: Date;
}

export interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  cacheControl?: string;
}

export interface StorageProvider {
  // 文件上传
  upload(key: string, file: File | Buffer, options?: UploadOptions): Promise<{ url: string; error?: Error }>;
  
  // 文件下载
  download(key: string): Promise<{ data: Blob | null; error?: Error }>;
  
  // 获取文件信息
  getFileInfo(key: string): Promise<{ file: StorageFile | null; error?: Error }>;
  
  // 获取公开访问URL
  getPublicUrl(key: string): string;
  
  // 获取签名URL（临时访问）
  getSignedUrl(key: string, expiresIn?: number): Promise<{ url: string; error?: Error }>;
  
  // 删除文件
  delete(key: string): Promise<{ error?: Error }>;
  
  // 批量删除
  deleteMany(keys: string[]): Promise<{ errors: (Error | null)[] }>;
  
  // 列出文件
  list(prefix?: string, limit?: number): Promise<{ files: StorageFile[]; error?: Error }>;
}

// Cloudflare R2 适配器
export class CloudflareR2Provider implements StorageProvider {
  private bucketName: string;
  private publicUrl: string;
  
  constructor(
    private r2Client: any, // R2 客户端
    bucketName: string,
    publicUrl: string
  ) {
    this.bucketName = bucketName;
    this.publicUrl = publicUrl;
  }

  async upload(key: string, file: File | Buffer, options?: UploadOptions) {
    try {
      const uploadParams = {
        Bucket: this.bucketName,
        Key: key,
        Body: file,
        ContentType: options?.contentType || 'application/octet-stream',
        Metadata: options?.metadata,
        CacheControl: options?.cacheControl,
      };

      await this.r2Client.putObject(uploadParams);
      
      return {
        url: this.getPublicUrl(key),
      };
    } catch (error) {
      return {
        url: '',
        error: error as Error,
      };
    }
  }

  async download(key: string) {
    try {
      const response = await this.r2Client.getObject({
        Bucket: this.bucketName,
        Key: key,
      });

      // 将 R2 响应转换为 Blob
      const chunks: Uint8Array[] = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const data = new Blob(chunks, { type: response.ContentType });

      return { data };
    } catch (error) {
      return {
        data: null,
        error: error as Error,
      };
    }
  }

  async getFileInfo(key: string) {
    try {
      const response = await this.r2Client.headObject({
        Bucket: this.bucketName,
        Key: key,
      });

      const file: StorageFile = {
        key,
        url: this.getPublicUrl(key),
        size: response.ContentLength,
        contentType: response.ContentType,
        lastModified: response.LastModified,
      };

      return { file };
    } catch (error) {
      return {
        file: null,
        error: error as Error,
      };
    }
  }

  getPublicUrl(key: string): string {
    return `${this.publicUrl}/${key}`;
  }

  async getSignedUrl(key: string, expiresIn = 3600) {
    try {
      const url = await this.r2Client.getSignedUrl('getObject', {
        Bucket: this.bucketName,
        Key: key,
        Expires: expiresIn,
      });

      return { url };
    } catch (error) {
      return {
        url: '',
        error: error as Error,
      };
    }
  }

  async delete(key: string) {
    try {
      await this.r2Client.deleteObject({
        Bucket: this.bucketName,
        Key: key,
      });

      return {};
    } catch (error) {
      return { error: error as Error };
    }
  }

  async deleteMany(keys: string[]) {
    const errors: (Error | null)[] = [];

    for (const key of keys) {
      const { error } = await this.delete(key);
      errors.push(error || null);
    }

    return { errors };
  }

  async list(prefix?: string, limit = 1000) {
    try {
      const response = await this.r2Client.listObjectsV2({
        Bucket: this.bucketName,
        Prefix: prefix,
        MaxKeys: limit,
      });

      const files: StorageFile[] = response.Contents?.map((obj: any) => ({
        key: obj.Key,
        url: this.getPublicUrl(obj.Key),
        size: obj.Size,
        contentType: 'application/octet-stream', // R2 不返回 ContentType
        lastModified: obj.LastModified,
      })) || [];

      return { files };
    } catch (error) {
      return {
        files: [],
        error: error as Error,
      };
    }
  }
}

// AWS S3 适配器（未来可能使用）
export class AWSS3Provider implements StorageProvider {
  // 实现 S3 的存储接口
  // 这里只是示例，展示如何轻松切换存储提供商
  
  async upload(_key: string, _file: File | Buffer, _options?: UploadOptions): Promise<{ url: string; error?: Error }> {
    // S3 实现
    throw new Error('S3 provider not implemented yet');
  }

  async download(_key: string): Promise<{ data: Blob | null; error?: Error }> {
    throw new Error('S3 provider not implemented yet');
  }

  async getFileInfo(_key: string): Promise<{ file: StorageFile | null; error?: Error }> {
    throw new Error('S3 provider not implemented yet');
  }

  getPublicUrl(_key: string): string {
    throw new Error('S3 provider not implemented yet');
  }

  async getSignedUrl(_key: string, _expiresIn?: number): Promise<{ url: string; error?: Error }> {
    throw new Error('S3 provider not implemented yet');
  }

  async delete(_key: string): Promise<{ error?: Error }> {
    throw new Error('S3 provider not implemented yet');
  }

  async deleteMany(_keys: string[]): Promise<{ errors: (Error | null)[] }> {
    throw new Error('S3 provider not implemented yet');
  }

  async list(_prefix?: string, _limit?: number): Promise<{ files: StorageFile[]; error?: Error }> {
    throw new Error('S3 provider not implemented yet');
  }
}
